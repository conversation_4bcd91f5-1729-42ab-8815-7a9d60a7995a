{"version": 3, "file": "PuppeteerNode.js", "sourceRoot": "", "sources": ["../../../../src/node/PuppeteerNode.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EACL,OAAO,IAAI,yBAAyB,EACpC,cAAc,EACd,qBAAqB,EACrB,oBAAoB,EACpB,SAAS,GACV,MAAM,qBAAqB,CAAC;AAK7B,OAAO,EAA+B,SAAS,EAAC,MAAM,wBAAwB,CAAC;AAE/E,OAAO,EAAC,mBAAmB,EAAC,MAAM,iBAAiB,CAAC;AAGpD,OAAO,EAAC,cAAc,EAAC,MAAM,qBAAqB,CAAC;AACnD,OAAO,EAAC,eAAe,EAAC,MAAM,sBAAsB,CAAC;AAGrD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAkCG;AACH,MAAM,OAAO,aAAc,SAAQ,SAAS;IAC1C,SAAS,CAAmB;IAC5B,oBAAoB,CAAoB;IAExC;;OAEG;IACH,sBAAsB,CAAS;IAE/B;;OAEG;IACH,aAAa,GAAkB,EAAE,CAAC;IAElC;;OAEG;IACH,YACE,QAE2B;QAE3B,MAAM,EAAC,aAAa,EAAE,GAAG,cAAc,EAAC,GAAG,QAAQ,CAAC;QACpD,KAAK,CAAC,cAAc,CAAC,CAAC;QACtB,IAAI,aAAa,EAAE,CAAC;YAClB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACrC,CAAC;QACD,QAAQ,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC;YAC1C,KAAK,SAAS;gBACZ,IAAI,CAAC,sBAAsB,GAAG,mBAAmB,CAAC,OAAO,CAAC;gBAC1D,MAAM;YACR;gBACE,IAAI,CAAC,aAAa,CAAC,cAAc,GAAG,QAAQ,CAAC;gBAC7C,IAAI,CAAC,sBAAsB,GAAG,mBAAmB,CAAC,MAAM,CAAC;gBACzD,MAAM;QACV,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED;;;;;OAKG;IACM,OAAO,CAAC,OAAuB;QACtC,OAAO,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAChC,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAmCG;IACH,MAAM,CAAC,UAAyB,EAAE;QAChC,MAAM,EAAC,OAAO,GAAG,IAAI,CAAC,cAAc,EAAC,GAAG,OAAO,CAAC;QAChD,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC;QACpC,QAAQ,OAAO,EAAE,CAAC;YAChB,KAAK,QAAQ;gBACX,IAAI,CAAC,sBAAsB,GAAG,mBAAmB,CAAC,MAAM,CAAC;gBACzD,MAAM;YACR,KAAK,SAAS;gBACZ,IAAI,CAAC,sBAAsB,GAAG,mBAAmB,CAAC,OAAO,CAAC;gBAC1D,MAAM;YACR;gBACE,MAAM,IAAI,KAAK,CAAC,oBAAoB,OAAO,EAAE,CAAC,CAAC;QACnD,CAAC;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAC5C,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,OAAyB;QACpC,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,KAAK,OAAO,EAAE,CAAC;YACzD,OAAO,IAAI,CAAC,SAAS,CAAC;QACxB,CAAC;QACD,QAAQ,OAAO,EAAE,CAAC;YAChB,KAAK,QAAQ;gBACX,OAAO,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC;YAClC,KAAK,SAAS;gBACZ,OAAO,IAAI,eAAe,CAAC,IAAI,CAAC,CAAC;YACnC;gBACE,MAAM,IAAI,KAAK,CAAC,oBAAoB,OAAO,EAAE,CAAC,CAAC;QACnD,CAAC;IACH,CAAC;IAcD,cAAc,CAAC,aAAoD;QACjE,IAAI,aAAa,KAAK,SAAS,EAAE,CAAC;YAChC,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,cAAc,CAC/D,SAAS;YACT,mBAAmB,CAAC,KAAK,CAC1B,CAAC;QACJ,CAAC;QACD,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE,CAAC;YACtC,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,cAAc,CAC/C,aAAa;YACb,mBAAmB,CAAC,KAAK,CAC1B,CAAC;QACJ,CAAC;QACD,OAAO,IAAI,CAAC,YAAY,CACtB,aAAa,CAAC,OAAO,IAAI,IAAI,CAAC,mBAAmB,CAClD,CAAC,qBAAqB,CAAC,aAAa,CAAC,QAAQ,EAAE,mBAAmB,CAAC,KAAK,CAAC,CAAC;IAC7E,CAAC;IAED;;OAEG;IACH,IAAI,cAAc;QAChB,OAAO,CACL,IAAI,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,EAAE,OAAO;YACvD,IAAI,CAAC,sBAAuB,CAC7B,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACH,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,cAAc,CAAC;IAC1D,CAAC;IAED;;;;OAIG;IACH,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,IAAI,QAAQ,CAAC;IACvD,CAAC;IAED;;;;;;;OAOG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,mBAAmB,CAAC;IAClC,CAAC;IAED;;;;OAIG;IACH,WAAW,CAAC,UAAyB,EAAE;QACrC,OAAO,IAAI,CAAC,YAAY,CACtB,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,mBAAmB,CAC5C,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IACzB,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,KAAK,CAAC,SAAS;QACb,MAAM,QAAQ,GAAG,qBAAqB,EAAE,CAAC;QACzC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC5D,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,cAAe,CAAC;QACpD,MAAM,iBAAiB,GAAG,MAAM,oBAAoB,CAAC;YACnD,QAAQ;SACT,CAAC,CAAC;QAEH,MAAM,iBAAiB,GAIlB;YACH;gBACE,OAAO,EAAE,QAAQ;gBACjB,OAAO,EAAE,yBAAyB,CAAC,MAAM;gBACzC,cAAc,EAAE,EAAE;aACnB;YACD;gBACE,OAAO,EAAE,SAAS;gBAClB,OAAO,EAAE,yBAAyB,CAAC,OAAO;gBAC1C,cAAc,EAAE,EAAE;aACnB;SACF,CAAC;QAEF,4BAA4B;QAC5B,KAAK,MAAM,IAAI,IAAI,iBAAiB,EAAE,CAAC;YACrC,MAAM,GAAG,GACP,IAAI,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,OAAO;gBAC3C,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAEpC,IAAI,CAAC,cAAc,GAAG,MAAM,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC;QAC1E,CAAC;QAED,MAAM,oBAAoB,GAAG,IAAI,GAAG,CAClC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YAC9B,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;QACxD,CAAC,CAAC,CACH,CAAC;QAEF,MAAM,eAAe,GAAG,IAAI,GAAG,CAC7B,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YAC9B,OAAO,OAAO,CAAC,OAAO,CAAC;QACzB,CAAC,CAAC,CACH,CAAC;QAEF,KAAK,MAAM,gBAAgB,IAAI,iBAAiB,EAAE,CAAC;YACjD,kEAAkE;YAClE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAAE,CAAC;gBACnD,SAAS;YACX,CAAC;YACD,qEAAqE;YACrE,IACE,oBAAoB,CAAC,GAAG,CACtB,GAAG,gBAAgB,CAAC,OAAO,IAAI,gBAAgB,CAAC,OAAO,EAAE,CAC1D,EACD,CAAC;gBACD,SAAS;YACX,CAAC;YAED,MAAM,SAAS,CAAC;gBACd,OAAO,EAAE,gBAAgB,CAAC,OAAO;gBACjC,QAAQ;gBACR,QAAQ;gBACR,OAAO,EAAE,gBAAgB,CAAC,OAAO;aAClC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;CACF"}