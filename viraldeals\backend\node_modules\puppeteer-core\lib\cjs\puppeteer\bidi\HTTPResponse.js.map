{"version": 3, "file": "HTTPResponse.js", "sourceRoot": "", "sources": ["../../../../src/bidi/HTTPResponse.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,4DAAwE;AAExE,mDAAyD;AACzD,qEAA6D;AAC7D,yDAAmE;AAInE;;GAEG;IACU,gBAAgB;sBAAS,8BAAY;;;iBAArC,gBAAiB,SAAQ,WAAY;;;yCA8C/C,4CAA4B;YAC7B,0LAAS,aAAa,6DAKrB;;;QAnDD,MAAM,CAAC,IAAI,CACT,IAA+B,EAC/B,OAAwB,EACxB,YAAqB;YAErB,MAAM,QAAQ,GAAG,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;YACnE,QAAQ,CAAC,WAAW,EAAE,CAAC;YACvB,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,KAAK,GAXM,mDAAgB,CAWM;QACjC,QAAQ,CAAkB;QAC1B,gBAAgB,CAAmB;QACnC,aAAa,GAAG,KAAK,CAAC;QAEtB,YACE,IAA+B,EAC/B,OAAwB,EACxB,YAAqB;YAErB,KAAK,EAAE,CAAC;YACR,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;YAClB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;YACxB,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;YAElC,0CAA0C;YAC1C,MAAM,eAAe,GAAG,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACrD,IAAI,YAAY,IAAI,eAAe,EAAE,CAAC;gBACpC,IAAI,CAAC,gBAAgB,GAAG,IAAI,oCAAe,CACzC,eAAmD,CACpD,CAAC;YACJ,CAAC;QACH,CAAC;QAED,WAAW;YACT,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;gBACzB,IAAI,CAAC,QAAQ,CAAC,gBAAgB,GAAG,IAAI,CAAC;gBACtC,IAAI,CAAC,QAAQ;qBACV,KAAK,EAAE;oBACR,EAAE,IAAI,EAAE;qBACP,cAAc,CAAC,IAAI,kEAAmC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC1E,CAAC;YACD,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,CAAC,cAAc,CAAC,IAAI,sCAAqB,IAAI,CAAC,CAAC;QAC9E,CAAC;QAGQ,aAAa;YACpB,OAAO;gBACL,EAAE,EAAE,EAAE;gBACN,IAAI,EAAE,CAAC,CAAC;aACT,CAAC;QACJ,CAAC;QAEQ,GAAG;YACV,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QACxB,CAAC;QAEQ,MAAM;YACb,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;QAC3B,CAAC;QAEQ,UAAU;YACjB,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;QAC/B,CAAC;QAEQ,OAAO;YACd,MAAM,OAAO,GAA2B,EAAE,CAAC;YAC3C,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;gBACxC,qCAAqC;gBACrC,4DAA4D;gBAC5D,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBACnC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;gBAC1D,CAAC;YACH,CAAC;YACD,OAAO,OAAO,CAAC;QACjB,CAAC;QAEQ,OAAO;YACd,OAAO,IAAI,CAAC,QAAQ,CAAC;QACvB,CAAC;QAEQ,SAAS;YAChB,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;QAC9B,CAAC;QAEQ,MAAM;YACb,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;YAC1C,OAAO;gBACL,WAAW,EAAE,UAAU,CAAC,WAAW;gBACnC,UAAU,EAAE,CAAC,CAAC;gBACd,QAAQ,EAAE,CAAC,CAAC;gBACZ,QAAQ,EAAE,UAAU,CAAC,QAAQ;gBAC7B,MAAM,EAAE,UAAU,CAAC,MAAM;gBACzB,YAAY,EAAE,UAAU,CAAC,YAAY;gBACrC,UAAU,EAAE,UAAU,CAAC,UAAU;gBACjC,QAAQ,EAAE,UAAU,CAAC,QAAQ;gBAC7B,MAAM,EAAE,CAAC,CAAC;gBACV,WAAW,EAAE,CAAC,CAAC;gBACf,WAAW,EAAE,CAAC,CAAC;gBACf,gBAAgB,EAAE,CAAC,CAAC;gBACpB,wBAAwB,EAAE,CAAC,CAAC;gBAC5B,2BAA2B,EAAE,CAAC,CAAC;gBAC/B,sBAAsB,EAAE,CAAC,CAAC;gBAC1B,SAAS,EAAE,UAAU,CAAC,YAAY;gBAClC,OAAO,EAAE,CAAC,CAAC;gBACX,SAAS,EAAE,CAAC,CAAC;gBACb,OAAO,EAAE,CAAC,CAAC;gBACX,mBAAmB,EAAE,UAAU,CAAC,aAAa;gBAC7C,iBAAiB,EAAE,UAAU,CAAC,WAAW;aAC1C,CAAC;QACJ,CAAC;QAEQ,KAAK;YACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QAC/B,CAAC;QAEQ,iBAAiB;YACxB,OAAO,KAAK,CAAC;QACf,CAAC;QAEQ,eAAe;YACtB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;gBACxB,MAAM,IAAI,gCAAoB,EAAE,CAAC;YACnC,CAAC;YACD,OAAO,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC;QACvC,CAAC;QAEQ,OAAO;YACd,MAAM,IAAI,gCAAoB,EAAE,CAAC;QACnC,CAAC;;;AAlIU,4CAAgB"}