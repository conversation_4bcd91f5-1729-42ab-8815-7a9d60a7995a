/**
 * @license
 * Copyright 2024 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * @internal
 */
export declare function stringToTypedArray(string: string, base64Encoded?: boolean): Uint8Array;
/**
 * @internal
 */
export declare function stringToBase64(str: string): string;
/**
 * @internal
 */
export declare function typedArrayToBase64(typedArray: Uint8Array): string;
/**
 * @internal
 */
export declare function mergeUint8Arrays(items: Uint8Array[]): Uint8Array;
//# sourceMappingURL=encoding.d.ts.map