{"version": 3, "file": "disposable.js", "sourceRoot": "", "sources": ["../../../../src/util/disposable.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AA0BF,MAAc,CAAC,OAAO,KAAK,MAAM,CAAC,SAAS,CAAC,CAAC;AAC7C,MAAc,CAAC,YAAY,KAAK,MAAM,CAAC,cAAc,CAAC,CAAC;AAExD;;GAEG;AACU,QAAA,aAAa,GAA0B,MAAM,CAAC,OAAO,CAAC;AAEnE;;GAEG;AACU,QAAA,kBAAkB,GAC7B,MAAM,CAAC,YAAY,CAAC;AAEtB;;GAEG;AACH,MAAa,eAAe;IAC1B,SAAS,GAAG,KAAK,CAAC;IAClB,MAAM,GAAiB,EAAE,CAAC;IAE1B;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,OAAO;QACL,IAAI,CAAC,qBAAa,CAAC,EAAE,CAAC;IACxB,CAAC;IAED;;;;;;;OAOG;IACH,GAAG,CAA0C,KAAQ;QACnD,IAAI,KAAK,IAAI,OAAO,KAAK,CAAC,qBAAa,CAAC,KAAK,UAAU,EAAE,CAAC;YACxD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAI,KAAQ,EAAE,SAA6B;QAC9C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACf,CAAC,qBAAa,CAAC;gBACb,SAAS,CAAC,KAAK,CAAC,CAAC;YACnB,CAAC;SACF,CAAC,CAAC;QACH,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,SAAqB;QACzB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACf,CAAC,qBAAa,CAAC;gBACb,SAAS,EAAE,CAAC;YACd,CAAC;SACF,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgCG;IACH,IAAI;QACF,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,IAAI,cAAc,CAAC,2CAA2C,CAAC,CAAC;QACxE,CAAC;QACD,MAAM,KAAK,GAAG,IAAI,eAAe,EAAE,CAAC;QACpC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,CAAC,qBAAa,CAAC;QACb,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,OAAO;QACT,CAAC;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,MAAM,MAAM,GAAc,EAAE,CAAC;QAC7B,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;YAC7C,IAAI,CAAC;gBACH,QAAQ,CAAC,qBAAa,CAAC,EAAE,CAAC;YAC5B,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC;QACH,CAAC;QACD,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,MAAM,MAAM,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;aAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,IAAI,UAAU,GAAG,IAAI,CAAC;YACtB,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;gBACrC,IAAI,UAAU,KAAK,IAAI,EAAE,CAAC;oBACxB,UAAU,GAAG,KAAK,CAAC;gBACrB,CAAC;qBAAM,CAAC;oBACN,UAAU,GAAG,IAAI,eAAe,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gBACtD,CAAC;YACH,CAAC;YACD,MAAM,UAAU,CAAC;QACnB,CAAC;IACH,CAAC;IAEQ,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,iBAAiB,CAAC;CACnD;AA1ID,0CA0IC;AAED;;GAEG;AACH,MAAa,oBAAoB;IAC/B,SAAS,GAAG,KAAK,CAAC;IAClB,MAAM,GAAsB,EAAE,CAAC;IAE/B;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO;QACX,MAAM,IAAI,CAAC,0BAAkB,CAAC,EAAE,CAAC;IACnC,CAAC;IAED;;;;;;;OAOG;IACH,GAAG,CAA4D,KAAQ;QACrE,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,YAAY,GAAI,KAAyB,CAAC,0BAAkB,CAAC,CAAC;YACpE,MAAM,OAAO,GAAI,KAAoB,CAAC,qBAAa,CAAC,CAAC;YAErD,IAAI,OAAO,YAAY,KAAK,UAAU,EAAE,CAAC;gBACvC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAwB,CAAC,CAAC;YAC7C,CAAC;iBAAM,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE,CAAC;gBACzC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;oBACf,CAAC,0BAAkB,CAAC,EAAE,KAAK,IAAI,EAAE;wBAC9B,KAAoB,CAAC,qBAAa,CAAC,EAAE,CAAC;oBACzC,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAI,KAAQ,EAAE,SAAsC;QACvD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACf,CAAC,0BAAkB,CAAC;gBAClB,OAAO,SAAS,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC;SACF,CAAC,CAAC;QACH,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,SAA8B;QAClC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACf,CAAC,0BAAkB,CAAC;gBAClB,OAAO,SAAS,EAAE,CAAC;YACrB,CAAC;SACF,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgCG;IACH,IAAI;QACF,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,IAAI,cAAc,CAAC,2CAA2C,CAAC,CAAC;QACxE,CAAC;QACD,MAAM,KAAK,GAAG,IAAI,oBAAoB,EAAE,CAAC;QACzC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,CAAC,0BAAkB,CAAC;QACxB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,OAAO;QACT,CAAC;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,MAAM,MAAM,GAAc,EAAE,CAAC;QAC7B,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;YAC7C,IAAI,CAAC;gBACH,MAAM,QAAQ,CAAC,0BAAkB,CAAC,EAAE,CAAC;YACvC,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC;QACH,CAAC;QACD,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,MAAM,MAAM,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;aAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,IAAI,UAAU,GAAG,IAAI,CAAC;YACtB,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;gBACrC,IAAI,UAAU,KAAK,IAAI,EAAE,CAAC;oBACxB,UAAU,GAAG,KAAK,CAAC;gBACrB,CAAC;qBAAM,CAAC;oBACN,UAAU,GAAG,IAAI,eAAe,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gBACtD,CAAC;YACH,CAAC;YACD,MAAM,UAAU,CAAC;QACnB,CAAC;IACH,CAAC;IAEQ,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,sBAAsB,CAAC;CACxD;AAtJD,oDAsJC;AAED;;;;;GAKG;AACH,MAAa,eAAgB,SAAQ,KAAK;IACxC,MAAM,CAAU;IAChB,WAAW,CAAU;IAErB,YACE,KAAc,EACd,UAAmB,EACnB,OAAO,GAAG,yCAAyC;QAEnD,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,iBAAiB,CAAC;QAC9B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED;;;OAGG;IACH,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;CACF;AA7BD,0CA6BC"}