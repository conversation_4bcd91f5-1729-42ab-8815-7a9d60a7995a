{"version": 3, "file": "EmulationManager.js", "sourceRoot": "", "sources": ["../../../../src/cdp/EmulationManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,OAAO,EAAkB,eAAe,EAAC,MAAM,sBAAsB,CAAC;AAEtE,OAAO,EAAC,UAAU,EAAC,MAAM,mBAAmB,CAAC;AAE7C,OAAO,EAAC,MAAM,EAAC,MAAM,mBAAmB,CAAC;AACzC,OAAO,EAAC,4BAA4B,EAAC,MAAM,uBAAuB,CAAC;AACnE,OAAO,EAAC,WAAW,EAAC,MAAM,sBAAsB,CAAC;AA+DjD;;GAEG;AACH,MAAM,OAAO,aAAa;IACxB,MAAM,CAAI;IACV,eAAe,CAAiB;IAChC,QAAQ,CAAkD;IAE1D,YACE,YAAe,EACf,cAA8B,EAC9B,OAAwD;QAExD,IAAI,CAAC,MAAM,GAAG,YAAY,CAAC;QAC3B,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,KAAQ;QACrB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;IACpB,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED,KAAK,CAAC,IAAI;QACR,MAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YAC1C,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC5C,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;CACF;AAED;;GAEG;IACU,gBAAgB;;;;;;;;;;;;;;;;;;;;;;iBAAhB,gBAAgB;;;iDA+I1B,4BAA4B;oDA2D5B,4BAA4B;mDAkB5B,4BAA4B;2DA2B5B,4BAA4B;wDAqC5B,4BAA4B;wDAwB5B,4BAA4B;oDA+B5B,4BAA4B;kDA0B5B,4BAA4B;6DA+C5B,4BAA4B;wDAiC5B,4BAA4B;YA7S7B,yDAAA,yBAAA,KAAK,WACH,MAAkB,EAClB,aAA4B;oBAE5B,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;wBAC5B,MAAM,OAAO,CAAC,GAAG,CAAC;4BAChB,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC;4BACnD,MAAM,CAAC,IAAI,CAAC,oCAAoC,EAAE;gCAChD,OAAO,EAAE,KAAK;6BACf,CAAC;yBACH,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;wBACrB,OAAO;oBACT,CAAC;oBACD,MAAM,EAAC,QAAQ,EAAC,GAAG,aAAa,CAAC;oBACjC,MAAM,MAAM,GAAG,QAAQ,CAAC,QAAQ,IAAI,KAAK,CAAC;oBAC1C,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;oBAC7B,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;oBAC/B,MAAM,iBAAiB,GAAG,QAAQ,CAAC,iBAAiB,IAAI,CAAC,CAAC;oBAC1D,MAAM,iBAAiB,GACrB,QAAQ,CAAC,WAAW;wBAClB,CAAC,CAAC,EAAC,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,kBAAkB,EAAC;wBACvC,CAAC,CAAC,EAAC,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,iBAAiB,EAAC,CAAC;oBAC1C,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,IAAI,KAAK,CAAC;oBAE5C,MAAM,OAAO,CAAC,GAAG,CAAC;wBAChB,MAAM;6BACH,IAAI,CAAC,oCAAoC,EAAE;4BAC1C,MAAM;4BACN,KAAK;4BACL,MAAM;4BACN,iBAAiB;4BACjB,iBAAiB;yBAClB,CAAC;6BACD,KAAK,CAAC,GAAG,CAAC,EAAE;4BACX,IACE,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,0CAA0C,CAAC,EAChE,CAAC;gCACD,UAAU,CAAC,GAAG,CAAC,CAAC;gCAChB,OAAO;4BACT,CAAC;4BACD,MAAM,GAAG,CAAC;wBACZ,CAAC,CAAC;wBACJ,MAAM,CAAC,IAAI,CAAC,oCAAoC,EAAE;4BAChD,OAAO,EAAE,QAAQ;yBAClB,CAAC;qBACH,CAAC,CAAC;gBACL,CAAC,mBAAA,qIA9CK,cAAc,yBAAd,cAAc,6DA8CnB;YAaD,4DAAA,yBAAA,KAAK,WACH,MAAkB,EAClB,cAAkC;oBAElC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;wBAC3B,OAAO;oBACT,CAAC;oBACD,IAAI,cAAc,CAAC,SAAS,EAAE,CAAC;wBAC7B,MAAM,MAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE;4BAC7C,YAAY,EAAE,cAAc,CAAC,SAAS,CAAC,YAAY;4BACnD,gBAAgB,EAAE,cAAc,CAAC,SAAS,CAAC,gBAAgB;yBAC5D,CAAC,CAAC;oBACL,CAAC;yBAAM,CAAC;wBACN,MAAM,MAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;oBACnD,CAAC;gBACH,CAAC,sBAAA,2IAfK,iBAAiB,yBAAjB,iBAAiB,6DAetB;YAGD,2DAAA,yBAAA,KAAK,WACH,MAAkB,EAClB,aAA4B;oBAE5B,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;wBAC1B,OAAO;oBACT,CAAC;oBACD,IAAI,CAAC;wBACH,MAAM,MAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE;4BACjD,UAAU,EAAE,aAAa,CAAC,UAAU,IAAI,EAAE;yBAC3C,CAAC,CAAC;oBACL,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,IAAI,WAAW,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE,CAAC;4BACrE,MAAM,IAAI,KAAK,CAAC,wBAAwB,aAAa,CAAC,UAAU,EAAE,CAAC,CAAC;wBACtE,CAAC;wBACD,MAAM,KAAK,CAAC;oBACd,CAAC;gBACH,CAAC,qBAAA,yIAjBK,gBAAgB,yBAAhB,gBAAgB,6DAiBrB;YAUD,mEAAA,yBAAA,KAAK,WACH,MAAkB,EAClB,gBAAuC;oBAEvC,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;wBAC7B,OAAO;oBACT,CAAC;oBACD,MAAM,MAAM,CAAC,IAAI,CAAC,uCAAuC,EAAE;wBACzD,IAAI,EAAE,gBAAgB,CAAC,gBAAgB,IAAI,MAAM;qBAClD,CAAC,CAAC;gBACL,CAAC,6BAAA,yJAVK,wBAAwB,yBAAxB,wBAAwB,6DAU7B;YA2BD,gEAAA,yBAAA,KAAK,WACH,MAAkB,EAClB,KAAyB;oBAEzB,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;wBAClB,OAAO;oBACT,CAAC;oBACD,MAAM,MAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE;wBAClD,IAAI,EAAE,KAAK,CAAC,MAAM,IAAI,CAAC;qBACxB,CAAC,CAAC;gBACL,CAAC,0BAAA,mJAVK,qBAAqB,yBAArB,qBAAqB,6DAU1B;YAcD,gEAAA,yBAAA,KAAK,WACH,MAAkB,EAClB,KAAyB;oBAEzB,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;wBAClB,OAAO;oBACT,CAAC;oBACD,MAAM,MAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE;wBAC9C,QAAQ,EAAE,KAAK,CAAC,aAAa;qBAC9B,CAAC,CAAC;gBACL,CAAC,0BAAA,mJAVK,qBAAqB,yBAArB,qBAAqB,6DAU1B;YAqBD,4DAAA,yBAAA,KAAK,WACH,MAAkB,EAClB,KAAqB;oBAErB,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;wBAClB,OAAO;oBACT,CAAC;oBACD,MAAM,MAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE;wBAC9C,KAAK,EAAE,KAAK,CAAC,IAAI,IAAI,EAAE;qBACxB,CAAC,CAAC;gBACL,CAAC,sBAAA,2IAVK,iBAAiB,yBAAjB,iBAAiB,6DAUtB;YAgBD,0DAAA,yBAAA,KAAK,WACH,MAAkB,EAClB,KAAuB;oBAEvB,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;wBAClB,OAAO;oBACT,CAAC;oBACD,MAAM,MAAM,CAAC,IAAI,CACf,kCAAkC,EAClC,KAAK,CAAC,WAAW;wBACf,CAAC,CAAC;4BACE,SAAS,EAAE,KAAK,CAAC,WAAW,CAAC,SAAS;4BACtC,QAAQ,EAAE,KAAK,CAAC,WAAW,CAAC,QAAQ;4BACpC,QAAQ,EAAE,KAAK,CAAC,WAAW,CAAC,QAAQ;yBACrC;wBACH,CAAC,CAAC,SAAS,CACd,CAAC;gBACJ,CAAC,oBAAA,uIAjBK,eAAe,yBAAf,eAAe,6DAiBpB;YA8BD,qEAAA,yBAAA,KAAK,WACH,MAAkB,EAClB,KAAkC;oBAElC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;wBAClB,OAAO;oBACT,CAAC;oBACD,MAAM,MAAM,CAAC,IAAI,CAAC,6CAA6C,EAAE;wBAC/D,KAAK,EAAE,KAAK,CAAC,KAAK;qBACnB,CAAC,CAAC;gBACL,CAAC,+BAAA,6JAVK,0BAA0B,yBAA1B,0BAA0B,6DAU/B;YAuBD,gEAAA,yBAAA,KAAK,WACH,MAAkB,EAClB,KAA6B;oBAE7B,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;wBAClB,OAAO;oBACT,CAAC;oBACD,MAAM,MAAM,CAAC,IAAI,CAAC,sCAAsC,EAAE;wBACxD,KAAK,EAAE,CAAC,KAAK,CAAC,iBAAiB;qBAChC,CAAC,CAAC;gBACL,CAAC,0BAAA,mJAVK,qBAAqB,yBAArB,qBAAqB,6DAU1B;;;QAvcD,OAAO,GADI,mDAAgB,CACP;QAEpB,gBAAgB,GAAG,KAAK,CAAC;QACzB,SAAS,GAAG,KAAK,CAAC;QAElB,OAAO,GAA8B,EAAE,CAAC;QAExC,cAAc,GAAG,IAAI,aAAa,CAChC;YACE,MAAM,EAAE,KAAK;SACd,EACD,IAAI,EACJ,IAAI,CAAC,cAAc,CACpB,CAAC;QACF,mBAAmB,GAAG,IAAI,aAAa,CACrC;YACE,MAAM,EAAE,KAAK;SACd,EACD,IAAI,EACJ,IAAI,CAAC,iBAAiB,CACvB,CAAC;QACF,cAAc,GAAG,IAAI,aAAa,CAChC;YACE,MAAM,EAAE,KAAK;SACd,EACD,IAAI,EACJ,IAAI,CAAC,gBAAgB,CACtB,CAAC;QACF,sBAAsB,GAAG,IAAI,aAAa,CACxC;YACE,MAAM,EAAE,KAAK;SACd,EACD,IAAI,EACJ,IAAI,CAAC,wBAAwB,CAC9B,CAAC;QACF,mBAAmB,GAAG,IAAI,aAAa,CACrC;YACE,MAAM,EAAE,KAAK;SACd,EACD,IAAI,EACJ,IAAI,CAAC,qBAAqB,CAC3B,CAAC;QACF,mBAAmB,GAAG,IAAI,aAAa,CACrC;YACE,MAAM,EAAE,KAAK;SACd,EACD,IAAI,EACJ,IAAI,CAAC,qBAAqB,CAC3B,CAAC;QACF,eAAe,GAAG,IAAI,aAAa,CACjC;YACE,MAAM,EAAE,KAAK;SACd,EACD,IAAI,EACJ,IAAI,CAAC,iBAAiB,CACvB,CAAC;QACF,iBAAiB,GAAG,IAAI,aAAa,CACnC;YACE,MAAM,EAAE,KAAK;SACd,EACD,IAAI,EACJ,IAAI,CAAC,eAAe,CACrB,CAAC;QACF,4BAA4B,GAAG,IAAI,aAAa,CAC9C;YACE,MAAM,EAAE,KAAK;SACd,EACD,IAAI,EACJ,IAAI,CAAC,0BAA0B,CAChC,CAAC;QACF,uBAAuB,GAAG,IAAI,aAAa,CACzC;YACE,iBAAiB,EAAE,IAAI;YACvB,MAAM,EAAE,KAAK;SACd,EACD,IAAI,EACJ,IAAI,CAAC,qBAAqB,CAC3B,CAAC;QAEF,iBAAiB,GAAG,IAAI,GAAG,EAAc,CAAC;QAE1C,YAAY,MAAkB;YAC5B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACxB,CAAC;QAED,YAAY,CAAC,MAAkB;YAC7B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;YACtB,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACxC,CAAC;QAED,aAAa,CAAC,KAAyB;YACrC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3B,CAAC;QAED,OAAO;YACL,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;QAC/D,CAAC;QAED,KAAK,CAAC,0BAA0B,CAAC,MAAkB;YACjD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YACnC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,GAAG,EAAE;gBAC7C,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC;YACH,2EAA2E;YAC3E,0BAA0B;YAC1B,KAAK,OAAO,CAAC,GAAG,CACd,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;gBACnB,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YACpC,CAAC,CAAC,CACH,CAAC;QACJ,CAAC;QAED,IAAI,iBAAiB;YACnB,OAAO,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,iBAAiB,CAAC;QAC9D,CAAC;QAED,KAAK,CAAC,eAAe,CAAC,QAAyB;YAC7C,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;YAC/C,IAAI,CAAC,QAAQ,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;gBACtC,OAAO,KAAK,CAAC;YACf,CAAC;YACD,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAChC,QAAQ;gBACN,CAAC,CAAC;oBACE,QAAQ;oBACR,MAAM,EAAE,IAAI;iBACb;gBACH,CAAC,CAAC;oBACE,MAAM,EAAE,KAAK;iBACd,CACN,CAAC;YAEF,MAAM,MAAM,GAAG,QAAQ,EAAE,QAAQ,IAAI,KAAK,CAAC;YAC3C,MAAM,QAAQ,GAAG,QAAQ,EAAE,QAAQ,IAAI,KAAK,CAAC;YAC7C,MAAM,YAAY,GAChB,IAAI,CAAC,gBAAgB,KAAK,MAAM,IAAI,IAAI,CAAC,SAAS,KAAK,QAAQ,CAAC;YAClE,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC;YAC/B,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;YAE1B,OAAO,YAAY,CAAC;QACtB,CAAC;QAGD,IAAM,cAAc,sDA8CnB;QAED,KAAK,CAAC,gBAAgB,CAAC,SAGtB;YACC,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC;gBACtC,MAAM,EAAE,IAAI;gBACZ,SAAS;aACV,CAAC,CAAC;QACL,CAAC;QAGD,IAAM,iBAAiB,yDAetB;QAGD,IAAM,gBAAgB,wDAiBrB;QAED,KAAK,CAAC,eAAe,CAAC,UAAmB;YACvC,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC;gBACjC,UAAU;gBACV,MAAM,EAAE,IAAI;aACb,CAAC,CAAC;QACL,CAAC;QAGD,IAAM,wBAAwB,gEAU7B;QAED,KAAK,CAAC,uBAAuB,CAC3B,IAAoE;YAEpE,MAAM,kBAAkB,GAAG,IAAI,GAAG,CAEhC;gBACA,MAAM;gBACN,eAAe;gBACf,eAAe;gBACf,cAAc;gBACd,YAAY;gBACZ,iBAAiB;gBACjB,YAAY;aACb,CAAC,CAAC;YACH,MAAM,CACJ,CAAC,IAAI,IAAI,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,EACrC,kCAAkC,IAAI,EAAE,CACzC,CAAC;YACF,MAAM,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC;gBACzC,MAAM,EAAE,IAAI;gBACZ,gBAAgB,EAAE,IAAI;aACvB,CAAC,CAAC;QACL,CAAC;QAGD,IAAM,qBAAqB,6DAU1B;QAED,KAAK,CAAC,oBAAoB,CAAC,MAAqB;YAC9C,MAAM,CACJ,MAAM,KAAK,IAAI,IAAI,MAAM,IAAI,CAAC,EAC9B,iDAAiD,CAClD,CAAC;YACF,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC;gBACtC,MAAM,EAAE,IAAI;gBACZ,MAAM,EAAE,MAAM,IAAI,SAAS;aAC5B,CAAC,CAAC;QACL,CAAC;QAGD,IAAM,qBAAqB,6DAU1B;QAED,KAAK,CAAC,oBAAoB,CAAC,QAAyB;YAClD,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC5B,KAAK,MAAM,YAAY,IAAI,QAAQ,EAAE,CAAC;oBACpC,MAAM,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC;oBAC/B,MAAM,CACJ,2DAA2D,CAAC,IAAI,CAC9D,IAAI,CACL,EACD,6BAA6B,GAAG,IAAI,CACrC,CAAC;gBACJ,CAAC;YACH,CAAC;YACD,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC;gBACtC,MAAM,EAAE,IAAI;gBACZ,aAAa,EAAE,QAAQ;aACxB,CAAC,CAAC;QACL,CAAC;QAGD,IAAM,iBAAiB,yDAUtB;QAED,KAAK,CAAC,gBAAgB,CAAC,IAAa;YAClC,MAAM,CACJ,IAAI,KAAK,QAAQ;gBACf,IAAI,KAAK,OAAO;gBAChB,CAAC,IAAI,IAAI,SAAS,CAAC,KAAK,SAAS,EACnC,0BAA0B,GAAG,IAAI,CAClC,CAAC;YACF,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC;gBAClC,IAAI;gBACJ,MAAM,EAAE,IAAI;aACb,CAAC,CAAC;QACL,CAAC;QAGD,IAAM,eAAe,uDAiBpB;QAED,KAAK,CAAC,cAAc,CAAC,OAA2B;YAC9C,MAAM,EAAC,SAAS,EAAE,QAAQ,EAAE,QAAQ,GAAG,CAAC,EAAC,GAAG,OAAO,CAAC;YACpD,IAAI,SAAS,GAAG,CAAC,GAAG,IAAI,SAAS,GAAG,GAAG,EAAE,CAAC;gBACxC,MAAM,IAAI,KAAK,CACb,sBAAsB,SAAS,kDAAkD,CAClF,CAAC;YACJ,CAAC;YACD,IAAI,QAAQ,GAAG,CAAC,EAAE,IAAI,QAAQ,GAAG,EAAE,EAAE,CAAC;gBACpC,MAAM,IAAI,KAAK,CACb,qBAAqB,QAAQ,+CAA+C,CAC7E,CAAC;YACJ,CAAC;YACD,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CACb,qBAAqB,QAAQ,uCAAuC,CACrE,CAAC;YACJ,CAAC;YACD,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC;gBACpC,MAAM,EAAE,IAAI;gBACZ,WAAW,EAAE;oBACX,SAAS;oBACT,QAAQ;oBACR,QAAQ;iBACT;aACF,CAAC,CAAC;QACL,CAAC;QAGD,IAAM,0BAA0B,kEAU/B;QAED;;WAEG;QACH,KAAK,CAAC,2BAA2B;YAC/B,MAAM,IAAI,CAAC,4BAA4B,CAAC,QAAQ,CAAC;gBAC/C,MAAM,EAAE,IAAI;gBACZ,KAAK,EAAE,SAAS;aACjB,CAAC,CAAC;QACL,CAAC;QAED;;WAEG;QACH,KAAK,CAAC,6BAA6B;YACjC,MAAM,IAAI,CAAC,4BAA4B,CAAC,QAAQ,CAAC;gBAC/C,MAAM,EAAE,IAAI;gBACZ,KAAK,EAAE,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC;aAChC,CAAC,CAAC;QACL,CAAC;QAGD,IAAM,qBAAqB,6DAU1B;QAED,KAAK,CAAC,oBAAoB,CAAC,OAAgB;YACzC,MAAM,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC;gBAC1C,MAAM,EAAE,IAAI;gBACZ,iBAAiB,EAAE,OAAO;aAC3B,CAAC,CAAC;QACL,CAAC;;;SA/cU,gBAAgB"}