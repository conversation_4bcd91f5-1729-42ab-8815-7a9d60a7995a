{"name": "viraldeals-backend", "version": "1.0.0", "description": "Backend API for ViralDeals.online - Indian E-commerce Platform", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node utils/seeder.js", "seed:delete": "node utils/seeder.js -d", "test": "NODE_ENV=test jest", "test:watch": "NODE_ENV=test jest --watch", "test:coverage": "NODE_ENV=test jest --coverage"}, "keywords": ["ecommerce", "india", "mern", "api"], "author": "ViralDeals Team", "license": "MIT", "dependencies": {"axios": "^1.10.0", "bcryptjs": "^3.0.2", "cheerio": "^1.1.0", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^17.1.0", "express": "^4.21.2", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.2", "multer": "^2.0.1", "node-cache": "^5.1.2", "puppeteer": "^24.12.1", "redis": "^5.6.0", "robots-parser": "^3.0.1", "viraldeals-online": "file:..", "winston": "^3.17.0"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "babel-jest": "^29.7.0", "concurrently": "^9.2.0", "form-data": "^4.0.3", "jest": "^29.7.0", "node-fetch": "^3.3.2", "nodemon": "^3.1.10", "supertest": "^7.1.3"}}