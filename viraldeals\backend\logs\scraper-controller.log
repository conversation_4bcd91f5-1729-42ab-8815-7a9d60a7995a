{"level":"info","message":"Scraping product from URL: https://deodap.in/products/6168-hqt-909b-hair-straightener-used-while-massaging-hair-scalps-and-head-1","timestamp":"2025-07-10T13:26:12.185Z"}
{"level":"info","message":"<PERSON>raper initialized successfully","timestamp":"2025-07-10T13:26:13.098Z"}
{"level":"info","message":"Scraping product from URL: https://deodap.in/products/6168-hqt-909b-hair-straightener-used-while-massaging-hair-scalps-and-head-1","timestamp":"2025-07-10T13:28:21.424Z"}
{"level":"info","message":"Scraper initialized successfully","timestamp":"2025-07-10T13:28:21.965Z"}
{"level":"info","message":"Scraping and saving product from URL: https://deodap.in/products/6168-hqt-909b-hair-straightener-used-while-massaging-hair-scalps-and-head-1","timestamp":"2025-07-10T13:28:48.211Z"}
{"_message":"Product validation failed","errors":{"createdBy":{"kind":"required","message":"Path `createdBy` is required.","name":"ValidatorError","path":"createdBy","properties":{"message":"Path `createdBy` is required.","path":"createdBy","type":"required"}}},"level":"error","message":"Error in scrapeAndSaveProduct: Product validation failed: createdBy: Path `createdBy` is required.","stack":"ValidationError: Product validation failed: createdBy: Path `createdBy` is required.\n    at Document.invalidate (C:\\Users\\<USER>\\Documents\\augment-projects\\viraldeals\\backend\\node_modules\\mongoose\\lib\\document.js:3352:32)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\viraldeals\\backend\\node_modules\\mongoose\\lib\\document.js:3113:17\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\viraldeals\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1410:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-07-10T13:28:54.169Z"}
