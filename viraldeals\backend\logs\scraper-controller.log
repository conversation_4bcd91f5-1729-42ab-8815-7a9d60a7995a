{"level":"info","message":"Scraping product from URL: https://deodap.in/products/6168-hqt-909b-hair-straightener-used-while-massaging-hair-scalps-and-head-1","timestamp":"2025-07-10T13:26:12.185Z"}
{"level":"info","message":"<PERSON>raper initialized successfully","timestamp":"2025-07-10T13:26:13.098Z"}
{"level":"info","message":"Scraping product from URL: https://deodap.in/products/6168-hqt-909b-hair-straightener-used-while-massaging-hair-scalps-and-head-1","timestamp":"2025-07-10T13:28:21.424Z"}
{"level":"info","message":"Scraper initialized successfully","timestamp":"2025-07-10T13:28:21.965Z"}
{"level":"info","message":"Scraping and saving product from URL: https://deodap.in/products/6168-hqt-909b-hair-straightener-used-while-massaging-hair-scalps-and-head-1","timestamp":"2025-07-10T13:28:48.211Z"}
{"_message":"Product validation failed","errors":{"createdBy":{"kind":"required","message":"Path `createdBy` is required.","name":"ValidatorError","path":"createdBy","properties":{"message":"Path `createdBy` is required.","path":"createdBy","type":"required"}}},"level":"error","message":"Error in scrapeAndSaveProduct: Product validation failed: createdBy: Path `createdBy` is required.","stack":"ValidationError: Product validation failed: createdBy: Path `createdBy` is required.\n    at Document.invalidate (C:\\Users\\<USER>\\Documents\\augment-projects\\viraldeals\\backend\\node_modules\\mongoose\\lib\\document.js:3352:32)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\viraldeals\\backend\\node_modules\\mongoose\\lib\\document.js:3113:17\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\viraldeals\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1410:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-07-10T13:28:54.169Z"}
{"level":"info","message":"Scraping and saving product from URL: https://deodap.in/products/6168-hqt-909b-hair-straightener-used-while-massaging-hair-scalps-and-head-1","timestamp":"2025-07-10T13:29:35.656Z"}
{"level":"info","message":"Scraper initialized successfully","timestamp":"2025-07-10T13:29:36.159Z"}
{"level":"info","message":"Product saved successfully: Hqt-909B Hair Straightener Used While Massaging Hair Scalps And Head., Hair accessories","timestamp":"2025-07-10T13:29:44.226Z"}
{"level":"info","message":"Scraping product from URL: https://deodap.in/products/whl146_12116_ss_vacuum_travel_mug_1200ml_cpr","timestamp":"2025-07-10T13:44:21.246Z"}
{"level":"info","message":"Scraping and saving product from URL: https://deodap.in/products/whl146_12116_ss_vacuum_travel_mug_1200ml_cpr","timestamp":"2025-07-10T13:44:56.839Z"}
{"_message":"Product validation failed","errors":{"name":{"kind":"maxlength","message":"Product name cannot exceed 100 characters","name":"ValidatorError","path":"name","properties":{"maxlength":100,"message":"Product name cannot exceed 100 characters","path":"name","type":"maxlength","value":"Customize / personalized Premium 1200 ML Stainless Steel Vacuum Insulated Travel Mug with Straw and Handle (1200ML / 1 Pc)"},"value":"Customize / personalized Premium 1200 ML Stainless Steel Vacuum Insulated Travel Mug with Straw and Handle (1200ML / 1 Pc)"},"shortDescription":{"kind":"maxlength","message":"Short description cannot exceed 200 characters","name":"ValidatorError","path":"shortDescription","properties":{"maxlength":200,"message":"Short description cannot exceed 200 characters","path":"shortDescription","type":"maxlength","value":"Premium 1200 ML Stainless Steel Vacuum Insulated Travel Mug with Straw and Handle – Double Wall Thermos Tumbler for Hot and Cold Beverages, Office, Gym, and Outdoor Use Description :- Stay hydrated and energized all day with this large-capacity 1200 ML stainless steel insulated travel mug."},"value":"Premium 1200 ML Stainless Steel Vacuum Insulated Travel Mug with Straw and Handle – Double Wall Thermos Tumbler for Hot and Cold Beverages, Office, Gym, and Outdoor Use Description :- Stay hydrated and energized all day with this large-capacity 1200 ML stainless steel insulated travel mug."}},"level":"error","message":"Error in scrapeAndSaveProduct: Product validation failed: name: Product name cannot exceed 100 characters, shortDescription: Short description cannot exceed 200 characters","stack":"ValidationError: Product validation failed: name: Product name cannot exceed 100 characters, shortDescription: Short description cannot exceed 200 characters\n    at Document.invalidate (C:\\Users\\<USER>\\Documents\\augment-projects\\viraldeals\\backend\\node_modules\\mongoose\\lib\\document.js:3352:32)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\viraldeals\\backend\\node_modules\\mongoose\\lib\\document.js:3113:17\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\viraldeals\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1410:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-07-10T13:45:03.144Z"}
