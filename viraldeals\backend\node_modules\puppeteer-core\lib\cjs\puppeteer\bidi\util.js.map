{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../../../src/bidi/util.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;AAYH,sDA0CC;AAKD,wDAYC;AAnED,mDAAgE;AAChE,+CAA+C;AAE/C,uDAAmD;AAEnD;;GAEG;AACH,SAAgB,qBAAqB,CACnC,OAAqC;IAErC,IAAI,OAAO,CAAC,SAAS,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;QACvC,OAAO,kCAAgB,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IACzD,CAAC;IACD,MAAM,CAAC,IAAI,GAAG,EAAE,EAAE,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACvD,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACjC,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;IACjC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;IAElB,mDAAmD;IACnD,MAAM,UAAU,GAAG,EAAE,CAAC;IACtB,IAAI,OAAO,CAAC,UAAU,IAAI,UAAU,CAAC,MAAM,GAAG,KAAK,CAAC,eAAe,EAAE,CAAC;QACpE,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC;YAC5D,IACE,sBAAY,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC;gBACtC,KAAK,CAAC,GAAG,KAAK,sBAAY,CAAC,YAAY,EACvC,CAAC;gBACD,MAAM,GAAG,GAAG,sBAAY,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAC1C,UAAU,CAAC,OAAO,CAChB,UAAU,KAAK,CAAC,YAAY,IAAI,GAAG,CAAC,YAAY,KAC9C,GAAG,CAAC,YACN,OAAO,GAAG,CAAC,UAAU,iBAAiB,KAAK,CAAC,UAAU,IACpD,KAAK,CAAC,YACR,GAAG,CACJ,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,UAAU,CAAC,IAAI,CACb,UAAU,KAAK,CAAC,YAAY,IAAI,aAAa,KAAK,KAAK,CAAC,GAAG,IACzD,KAAK,CAAC,UACR,IAAI,KAAK,CAAC,YAAY,GAAG,CAC1B,CAAC;YACJ,CAAC;YACD,IAAI,UAAU,CAAC,MAAM,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;gBAC/C,MAAM;YACR,CAAC;QACH,CAAC;IACH,CAAC;IAED,KAAK,CAAC,KAAK,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvD,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;GAEG;AACH,SAAgB,sBAAsB,CACpC,OAAe,EACf,EAAU;IAEV,OAAO,KAAK,CAAC,EAAE;QACb,IAAI,KAAK,YAAY,yBAAa,EAAE,CAAC;YACnC,KAAK,CAAC,OAAO,IAAI,OAAO,OAAO,EAAE,CAAC;QACpC,CAAC;aAAM,IAAI,KAAK,YAAY,wBAAY,EAAE,CAAC;YACzC,KAAK,CAAC,OAAO,GAAG,yBAAyB,EAAE,cAAc,CAAC;QAC5D,CAAC;QACD,MAAM,KAAK,CAAC;IACd,CAAC,CAAC;AACJ,CAAC"}