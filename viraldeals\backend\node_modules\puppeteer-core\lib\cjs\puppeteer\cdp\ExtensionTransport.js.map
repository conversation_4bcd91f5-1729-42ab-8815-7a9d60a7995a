{"version": 3, "file": "ExtensionTransport.js", "sourceRoot": "", "sources": ["../../../../src/cdp/ExtensionTransport.ts"], "names": [], "mappings": ";;;AAOA,MAAM,aAAa,GAAG;IACpB,QAAQ,EAAE,aAAa;IACvB,IAAI,EAAE,KAAK;IACX,KAAK,EAAE,KAAK;IACZ,GAAG,EAAE,aAAa;IAClB,QAAQ,EAAE,KAAK;IACf,eAAe,EAAE,KAAK;CACvB,CAAC;AAEF,MAAM,cAAc,GAAG;IACrB,QAAQ,EAAE,cAAc;IACxB,IAAI,EAAE,MAAM;IACZ,KAAK,EAAE,MAAM;IACb,GAAG,EAAE,aAAa;IAClB,QAAQ,EAAE,KAAK;IACf,eAAe,EAAE,KAAK;CACvB,CAAC;AAEF;;;;;;;;GAQG;AACH,MAAa,kBAAkB;IAC7B,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,KAAa;QACnC,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAC,KAAK,EAAC,EAAE,KAAK,CAAC,CAAC;QAC7C,OAAO,IAAI,kBAAkB,CAAC,KAAK,CAAC,CAAC;IACvC,CAAC;IAED,SAAS,CAA6B;IACtC,OAAO,CAAc;IAErB,MAAM,CAAS;IAEf;;OAEG;IACH,YAAY,KAAa;QACvB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;IAClE,CAAC;IAED,qBAAqB,GAAG,CACtB,MAAgC,EAChC,MAAc,EACd,MAA2B,EACrB,EAAE;QACR,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC;YACjC,OAAO;QACT,CAAC;QACD,IAAI,CAAC,iBAAiB,CAAC;YACrB,mDAAmD;YACnD,SAAS,EAAE,MAAM,CAAC,SAAS,IAAI,qBAAqB;YACpD,MAAM,EAAE,MAAM;YACd,MAAM,EAAE,MAAM;SACf,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,iBAAiB,CAAC,OAAe;QAC/B,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;IAC5C,CAAC;IAED,IAAI,CAAC,OAAe;QAClB,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACnC,QAAQ,MAAM,CAAC,MAAM,EAAE,CAAC;YACtB,KAAK,oBAAoB,CAAC,CAAC,CAAC;gBAC1B,IAAI,CAAC,iBAAiB,CAAC;oBACrB,EAAE,EAAE,MAAM,CAAC,EAAE;oBACb,SAAS,EAAE,MAAM,CAAC,SAAS;oBAC3B,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,MAAM,EAAE;wBACN,eAAe,EAAE,KAAK;wBACtB,OAAO,EAAE,QAAQ;wBACjB,QAAQ,EAAE,SAAS;wBACnB,SAAS,EAAE,QAAQ;wBACnB,SAAS,EAAE,SAAS;qBACrB;iBACF,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YACD,KAAK,2BAA2B,CAAC,CAAC,CAAC;gBACjC,IAAI,CAAC,iBAAiB,CAAC;oBACrB,EAAE,EAAE,MAAM,CAAC,EAAE;oBACb,SAAS,EAAE,MAAM,CAAC,SAAS;oBAC3B,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,MAAM,EAAE;wBACN,iBAAiB,EAAE,EAAE;qBACtB;iBACF,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YACD,KAAK,2BAA2B,CAAC,CAAC,CAAC;gBACjC,IAAI,CAAC,iBAAiB,CAAC;oBACrB,MAAM,EAAE,sBAAsB;oBAC9B,MAAM,EAAE;wBACN,UAAU,EAAE,aAAa;qBAC1B;iBACF,CAAC,CAAC;gBACH,IAAI,CAAC,iBAAiB,CAAC;oBACrB,MAAM,EAAE,sBAAsB;oBAC9B,MAAM,EAAE;wBACN,UAAU,EAAE,cAAc;qBAC3B;iBACF,CAAC,CAAC;gBACH,IAAI,CAAC,iBAAiB,CAAC;oBACrB,EAAE,EAAE,MAAM,CAAC,EAAE;oBACb,SAAS,EAAE,MAAM,CAAC,SAAS;oBAC3B,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,MAAM,EAAE,EAAE;iBACX,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YACD,KAAK,sBAAsB,CAAC,CAAC,CAAC;gBAC5B,IAAI,MAAM,CAAC,SAAS,KAAK,oBAAoB,EAAE,CAAC;oBAC9C,IAAI,CAAC,iBAAiB,CAAC;wBACrB,MAAM,EAAE,yBAAyB;wBACjC,MAAM,EAAE;4BACN,UAAU,EAAE,cAAc;4BAC1B,SAAS,EAAE,qBAAqB;yBACjC;qBACF,CAAC,CAAC;oBACH,IAAI,CAAC,iBAAiB,CAAC;wBACrB,EAAE,EAAE,MAAM,CAAC,EAAE;wBACb,SAAS,EAAE,MAAM,CAAC,SAAS;wBAC3B,MAAM,EAAE,MAAM,CAAC,MAAM;wBACrB,MAAM,EAAE,EAAE;qBACX,CAAC,CAAC;oBACH,OAAO;gBACT,CAAC;qBAAM,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;oBAC7B,IAAI,CAAC,iBAAiB,CAAC;wBACrB,MAAM,EAAE,yBAAyB;wBACjC,MAAM,EAAE;4BACN,UAAU,EAAE,aAAa;4BACzB,SAAS,EAAE,oBAAoB;yBAChC;qBACF,CAAC,CAAC;oBACH,IAAI,CAAC,iBAAiB,CAAC;wBACrB,EAAE,EAAE,MAAM,CAAC,EAAE;wBACb,SAAS,EAAE,MAAM,CAAC,SAAS;wBAC3B,MAAM,EAAE,MAAM,CAAC,MAAM;wBACrB,MAAM,EAAE,EAAE;qBACX,CAAC,CAAC;oBACH,OAAO;gBACT,CAAC;YACH,CAAC;QACH,CAAC;QACD,IAAI,MAAM,CAAC,SAAS,KAAK,qBAAqB,EAAE,CAAC;YAC/C,OAAO,MAAM,CAAC,SAAS,CAAC;QAC1B,CAAC;QACD,MAAM,CAAC,QAAQ;aACZ,WAAW,CACV,EAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,SAAS,EAAC,EACjD,MAAM,CAAC,MAAM,EACb,MAAM,CAAC,MAAM,CACd;aACA,IAAI,CAAC,QAAQ,CAAC,EAAE;YACf,IAAI,CAAC,iBAAiB,CAAC;gBACrB,EAAE,EAAE,MAAM,CAAC,EAAE;gBACb,SAAS,EAAE,MAAM,CAAC,SAAS,IAAI,qBAAqB;gBACpD,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,MAAM,EAAE,QAAQ;aACjB,CAAC,CAAC;QACL,CAAC,CAAC;aACD,KAAK,CAAC,GAAG,CAAC,EAAE;YACX,IAAI,CAAC,iBAAiB,CAAC;gBACrB,EAAE,EAAE,MAAM,CAAC,EAAE;gBACb,SAAS,EAAE,MAAM,CAAC,SAAS,IAAI,qBAAqB;gBACpD,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,KAAK,EAAE;oBACL,IAAI,EAAE,GAAG,EAAE,IAAI;oBACf,IAAI,EAAE,GAAG,EAAE,IAAI;oBACf,OAAO,EAAE,GAAG,EAAE,OAAO,IAAI,0BAA0B;iBACpD;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED,KAAK;QACH,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACnE,KAAK,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAC,CAAC,CAAC;IACpD,CAAC;CACF;AA9JD,gDA8JC"}