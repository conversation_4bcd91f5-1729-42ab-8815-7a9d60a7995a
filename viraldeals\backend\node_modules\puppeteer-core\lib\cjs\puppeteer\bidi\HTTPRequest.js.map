{"version": 3, "file": "HTTPRequest.js", "sourceRoot": "", "sources": ["../../../../src/bidi/HTTPRequest.ts"], "names": [], "mappings": ";;;;AAaA,0DAK+B;AAE/B,mDAAyD;AACzD,qDAAmD;AAInD,uDAAmD;AAEtC,QAAA,QAAQ,GAAG,IAAI,OAAO,EAA4B,CAAC;AAEhE;;GAEG;AACH,MAAa,eAAgB,SAAQ,4BAAW;IAC9C,MAAM,CAAC,IAAI,CACT,WAAoB,EACpB,KAAgB,EAChB,QAA0B;QAE1B,MAAM,OAAO,GAAG,IAAI,EAAe,CAAC,WAAW,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QAClE,OAAO,CAAC,WAAW,EAAE,CAAC;QACtB,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,cAAc,CAAoB;IAClC,SAAS,GAA4B,IAAI,CAAC;IACxB,EAAE,CAAS;IACpB,MAAM,CAAY;IAClB,QAAQ,CAAU;IAE3B,YACE,OAAgB,EAChB,KAAgB,EAChB,QAA0B;QAE1B,KAAK,EAAE,CAAC;QACR,gBAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAE5B,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC;QAE9C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC;QAC9D,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;IACvB,CAAC;IAED,IAAa,MAAM;QACjB,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IAC5B,CAAC;IAED,WAAW;QACT,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE;YACrC,MAAM,WAAW,GAAG,EAAe,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YACrE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE/B,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,EAAE;gBAC3B,IAAI,CAAC,MAAM;qBACR,IAAI,EAAE;qBACN,cAAc,CAAC,IAAI,oDAA4B,WAAW,CAAC,CAAC;YACjE,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE;gBACzB,IAAI,CAAC,MAAM;qBACR,IAAI,EAAE;qBACN,cAAc,CAAC,IAAI,gDAA0B,WAAW,CAAC,CAAC;YAC/D,CAAC,CAAC,CAAC;YACH,KAAK,WAAW,CAAC,qBAAqB,EAAE,CAAC;QAC3C,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE;YACnC,IAAI,CAAC,SAAS,GAAG,kCAAgB,CAAC,IAAI,CACpC,IAAI,EACJ,IAAI,EACJ,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,YAAY,CAC1C,CAAC;QACJ,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,cAAc,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAE7D,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,cAAc,CAAC,IAAI,oCAAoB,IAAI,CAAC,CAAC;QAEhE,IAAI,IAAI,CAAC,2BAA2B,EAAE,CAAC;YACrC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE;gBACzC,MAAM,IAAI,CAAC,QAAQ,CACjB;oBACE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;iBACxB,EACD,CAAC,CACF,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEQ,GAAG;QACV,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;IAC3B,CAAC;IAEQ,YAAY;QACnB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,YAAY,EAAE,CAAC;YAC/C,MAAM,IAAI,gCAAoB,EAAE,CAAC;QACnC,CAAC;QACD,OAAO,CACL,IAAI,CAAC,QAAQ,CAAC,YAAY,IAAI,OAAO,CACtC,CAAC,WAAW,EAAkB,CAAC;IAClC,CAAC;IAEQ,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;IAC9B,CAAC;IAEQ,QAAQ;QACf,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,YAAY,EAAE,CAAC;YAC/C,MAAM,IAAI,gCAAoB,EAAE,CAAC;QACnC,CAAC;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;IAChC,CAAC;IAEQ,WAAW;QAClB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,YAAY,EAAE,CAAC;YAC/C,MAAM,IAAI,gCAAoB,EAAE,CAAC;QACnC,CAAC;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;IACnC,CAAC;IAEQ,KAAK,CAAC,aAAa;QAC1B,MAAM,IAAI,gCAAoB,EAAE,CAAC;IACnC,CAAC;IAED,IAAI,2BAA2B;QAC7B,OAAO,OAAO,CACZ,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,MAAM;YACxC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,MAAM,CAC7C,CAAC;IACJ,CAAC;IAED,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,iBAAiB,IAAI,EAAE,CAAC;IACrD,CAAC;IAED,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,iBAAiB,IAAI,EAAE,CAAC;IACrD,CAAC;IAEQ,OAAO;QACd,MAAM,OAAO,GAA2B,EAAE,CAAC;QAC3C,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;YAC3C,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;QAC1D,CAAC;QACD,OAAO;YACL,GAAG,OAAO;YACV,GAAG,IAAI,CAAC,iBAAiB;YACzB,GAAG,IAAI,CAAC,iBAAiB;SAC1B,CAAC;IACJ,CAAC;IAEQ,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEQ,OAAO;QACd,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;YACtC,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,EAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAC,CAAC;IAC1C,CAAC;IAEQ,mBAAmB;QAC1B,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,KAAK,SAAS,CAAC;IAChD,CAAC;IAEQ,SAAS;QAChB,OAAO;YACL,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS;YAC1B,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,IAAI,OAAO;SAC/C,CAAC;IACJ,CAAC;IAEQ,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;IACrC,CAAC;IAEQ,KAAK;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAEQ,KAAK,CAAC,QAAQ,CACrB,SAAoC,EACpC,QAA6B;QAE7B,OAAO,MAAM,KAAK,CAAC,QAAQ,CACzB;YACE,OAAO,EAAE,IAAI,CAAC,2BAA2B,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,SAAS;YACtE,GAAG,SAAS;SACb,EACD,QAAQ,CACT,CAAC;IACJ,CAAC;IAEQ,KAAK,CAAC,SAAS,CACtB,YAAsC,EAAE;QAExC,MAAM,OAAO,GAA0B,cAAc,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QACzE,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC;QAEjC,OAAO,MAAM,IAAI,CAAC,QAAQ;aACvB,eAAe,CAAC;YACf,GAAG,EAAE,SAAS,CAAC,GAAG;YAClB,MAAM,EAAE,SAAS,CAAC,MAAM;YACxB,IAAI,EAAE,SAAS,CAAC,QAAQ;gBACtB,CAAC,CAAC;oBACE,IAAI,EAAE,QAAQ;oBACd,KAAK,EAAE,IAAA,4BAAc,EAAC,SAAS,CAAC,QAAQ,CAAC;iBAC1C;gBACH,CAAC,CAAC,SAAS;YACb,OAAO,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;SAClD,CAAC;aACD,KAAK,CAAC,KAAK,CAAC,EAAE;YACb,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,KAAK,CAAC;YAClC,OAAO,IAAA,4BAAW,EAAC,KAAK,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;IACP,CAAC;IAEQ,KAAK,CAAC,MAAM;QACnB,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC;QACjC,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACrD,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,KAAK,CAAC;YAClC,MAAM,KAAK,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAEQ,KAAK,CAAC,QAAQ,CACrB,QAAqC,EACrC,SAAkB;QAElB,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC;QAEjC,IAAI,UAKS,CAAC;QACd,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;YAClB,UAAU,GAAG,4BAAW,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACtD,CAAC;QAED,MAAM,OAAO,GAA0B,cAAc,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QACxE,MAAM,gBAAgB,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YAC7C,OAAO,MAAM,CAAC,IAAI,KAAK,gBAAgB,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;YACzB,OAAO,CAAC,IAAI,CAAC;gBACX,IAAI,EAAE,cAAc;gBACpB,KAAK,EAAE;oBACL,IAAI,EAAE,QAAQ;oBACd,KAAK,EAAE,QAAQ,CAAC,WAAW;iBAC5B;aACF,CAAC,CAAC;QACL,CAAC;QAED,IAAI,UAAU,EAAE,aAAa,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACnD,OAAO,CAAC,IAAI,CAAC;gBACX,IAAI,EAAE,gBAAgB;gBACtB,KAAK,EAAE;oBACL,IAAI,EAAE,QAAQ;oBACd,KAAK,EAAE,MAAM,CAAC,UAAU,CAAC,aAAa,CAAC;iBACxC;aACF,CAAC,CAAC;QACL,CAAC;QACD,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,IAAI,GAAG,CAAC;QAEtC,OAAO,MAAM,IAAI,CAAC,QAAQ;aACvB,eAAe,CAAC;YACf,UAAU,EAAE,MAAM;YAClB,OAAO,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;YACjD,YAAY,EAAE,6BAAY,CAAC,MAAM,CAAC;YAClC,IAAI,EAAE,UAAU,EAAE,MAAM;gBACtB,CAAC,CAAC;oBACE,IAAI,EAAE,QAAQ;oBACd,KAAK,EAAE,UAAU,EAAE,MAAM;iBAC1B;gBACH,CAAC,CAAC,SAAS;SACd,CAAC;aACD,KAAK,CAAC,KAAK,CAAC,EAAE;YACb,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,KAAK,CAAC;YAClC,MAAM,KAAK,CAAC;QACd,CAAC,CAAC,CAAC;IACP,CAAC;IAED,sBAAsB,GAAG,KAAK,CAAC;IAC/B,qBAAqB,GAAG,KAAK,IAAI,EAAE;QACjC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO;QACT,CAAC;QACD,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,YAAY,CAAC;QACpD,IAAI,WAAW,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAChD,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;YACnC,KAAK,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC;gBAClC,MAAM,EAAE,oBAAoB;gBAC5B,WAAW,EAAE;oBACX,IAAI,EAAE,UAAU;oBAChB,QAAQ,EAAE,WAAW,CAAC,QAAQ;oBAC9B,QAAQ,EAAE,WAAW,CAAC,QAAQ;iBAC/B;aACF,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,KAAK,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC;gBAClC,MAAM,EAAE,QAAQ;aACjB,CAAC,CAAC;QACL,CAAC;IACH,CAAC,CAAC;IAEF,MAAM;QACJ,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;IAChC,CAAC;CACF;AA7SD,0CA6SC;;AAED,SAAS,cAAc,CAAC,UAAoC;IAC1D,MAAM,OAAO,GAA0B,EAAE,CAAC;IAC1C,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC,EAAE,CAAC;QAC7D,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE,CAAC;YACjC,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YAEtD,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBAC3B,OAAO,CAAC,IAAI,CAAC;oBACX,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE;oBACxB,KAAK,EAAE;wBACL,IAAI,EAAE,QAAQ;wBACd,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC;qBACrB;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC"}