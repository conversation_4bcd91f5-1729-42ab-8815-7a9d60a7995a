/**
 * @license
 * Copyright 2020 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * @public
 */
export interface Viewport {
    /**
     * The page width in CSS pixels.
     *
     * @remarks
     * Setting this value to `0` will reset this value to the system default.
     */
    width: number;
    /**
     * The page height in CSS pixels.
     *
     * @remarks
     * Setting this value to `0` will reset this value to the system default.
     */
    height: number;
    /**
     * Specify device scale factor.
     * See {@link https://developer.mozilla.org/en-US/docs/Web/API/Window/devicePixelRatio | devicePixelRatio} for more info.
     *
     * @remarks
     * Setting this value to `0` will reset this value to the system default.
     *
     * @defaultValue `1`
     */
    deviceScaleFactor?: number;
    /**
     * Whether the `meta viewport` tag is taken into account.
     * @defaultValue `false`
     */
    isMobile?: boolean;
    /**
     * Specifies if the viewport is in landscape mode.
     * @defaultValue `false`
     */
    isLandscape?: boolean;
    /**
     * Specify if the viewport supports touch events.
     * @defaultValue `false`
     */
    hasTouch?: boolean;
}
//# sourceMappingURL=Viewport.d.ts.map